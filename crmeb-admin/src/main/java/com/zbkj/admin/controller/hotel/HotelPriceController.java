package com.zbkj.admin.controller.hotel;

import com.github.pagehelper.PageInfo;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.hotel.HotelPriceCalendarRequest;
import com.zbkj.common.request.hotel.HotelPriceStrategyRequest;
import com.zbkj.common.request.hotel.HotelPriceStrategySearchRequest;
import com.zbkj.common.response.hotel.HotelPriceCalendarResponse;
import com.zbkj.common.response.hotel.HotelPriceStrategyResponse;
import com.zbkj.common.result.CommonResult;
import com.zbkj.service.service.hotel.HotelRoomPriceStrategyService;
import com.zbkj.service.service.hotel.HotelProductSyncService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 酒店价格策略管理控制器 - 商户端
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("api/admin/hotel/price")
@Api(tags = "商户端 - 酒店价格策略管理")
@Validated
public class HotelPriceController {

    @Autowired
    private HotelRoomPriceStrategyService priceStrategyService;

    @Autowired
    private HotelProductSyncService hotelProductSyncService;

    /**
     * 分页查询价格策略列表
     */
//    @PreAuthorize("hasAuthority('admin:hotel:price:list')")
    @ApiOperation(value = "分页查询价格策略列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<PageInfo<HotelPriceStrategyResponse>> getList(@Validated HotelPriceStrategySearchRequest request,
                                                                      @Validated PageParamRequest pageParamRequest) {
        PageInfo<HotelPriceStrategyResponse> pageInfo = priceStrategyService.getMerchantPage(request, pageParamRequest);
        return CommonResult.success(pageInfo);
    }

    /**
     * 新增价格策略
     */
//    @PreAuthorize("hasAuthority('admin:hotel:price:save')")
    @ApiOperation(value = "新增价格策略")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<String> save(@RequestBody @Valid HotelPriceStrategyRequest request) {
        if (priceStrategyService.savePriceStrategy(request)) {
            return CommonResult.success("新增价格策略成功");
        } else {
            return CommonResult.failed("新增价格策略失败");
        }
    }

    /**
     * 修改价格策略
     */
//    @PreAuthorize("hasAuthority('admin:hotel:price:update')")
    @ApiOperation(value = "修改价格策略")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> update(@RequestBody @Valid HotelPriceStrategyRequest request) {
        if (priceStrategyService.updatePriceStrategy(request)) {
            // 🔄 立即触发实时同步，确保价格策略修改后商品价格和标记立即更新
            try {
                hotelProductSyncService.syncRoomProductsRealTime(request.getRoomId());
                log.info("价格策略更新后实时同步完成，房型ID: {}", request.getRoomId());
            } catch (Exception e) {
                log.error("价格策略更新后实时同步失败，房型ID: {}", request.getRoomId(), e);
                // 不影响主流程，只记录错误
            }
            return CommonResult.success("修改价格策略成功");
        } else {
            return CommonResult.failed("修改价格策略失败");
        }
    }

    /**
     * 获取价格策略详情
     */
//    @PreAuthorize("hasAuthority('admin:hotel:price:info')")
    @ApiOperation(value = "获取价格策略详情")
    @RequestMapping(value = "/info/{id}", method = RequestMethod.GET)
    public CommonResult<HotelPriceStrategyResponse> info(@PathVariable @NotNull(message = "策略ID不能为空") Integer id) {
        HotelPriceStrategyResponse response = priceStrategyService.getStrategyInfo(id);
        return CommonResult.success(response);
    }

    /**
     * 删除价格策略
     */
//    @PreAuthorize("hasAuthority('admin:hotel:price:delete')")
    @ApiOperation(value = "删除价格策略")
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    public CommonResult<String> delete(@PathVariable @NotNull(message = "策略ID不能为空") Integer id) {
        if (priceStrategyService.deleteStrategy(id)) {
            return CommonResult.success("删除价格策略成功");
        } else {
            return CommonResult.failed("删除价格策略失败");
        }
    }

    /**
     * 修改价格策略状态
     */
//    @PreAuthorize("hasAuthority('admin:hotel:price:status')")
    @ApiOperation(value = "修改价格策略状态")
    @RequestMapping(value = "/status/{id}/{status}", method = RequestMethod.POST)
    public CommonResult<String> updateStatus(@PathVariable @NotNull(message = "策略ID不能为空") Integer id,
                                             @PathVariable @NotNull(message = "状态不能为空") Integer status) {
        if (status != 0 && status != 1) {
            return CommonResult.failed("状态值错误");
        }

        if (priceStrategyService.updateStrategyStatus(id, status)) {
            // 🔄 获取策略信息并触发实时同步，确保策略状态修改后商品标记立即更新
            try {
                // 获取策略信息以获取房型ID
                HotelPriceStrategyResponse strategyInfo = priceStrategyService.getStrategyInfo(id);
                if (strategyInfo != null && strategyInfo.getRoomId() != null) {
                    hotelProductSyncService.syncRoomProductsRealTime(strategyInfo.getRoomId());
                    log.info("价格策略状态更新后实时同步完成，策略ID: {}, 房型ID: {}, 新状态: {}",
                            id, strategyInfo.getRoomId(), status);
                }
            } catch (Exception e) {
                log.error("价格策略状态更新后实时同步失败，策略ID: {}, 新状态: {}", id, status, e);
                // 不影响主流程，只记录错误
            }
            return CommonResult.success("修改状态成功");
        } else {
            return CommonResult.failed("修改状态失败");
        }
    }

    /**
     * 获取价格日历
     */
//    @PreAuthorize("hasAuthority('admin:hotel:price:calendar')")
    @ApiOperation(value = "获取价格日历")
    @RequestMapping(value = "/calendar", method = RequestMethod.GET)
    public CommonResult<HotelPriceCalendarResponse> getPriceCalendar(@Validated HotelPriceCalendarRequest request) {
        HotelPriceCalendarResponse response = priceStrategyService.getPriceCalendar(request);
        return CommonResult.success(response);
    }
}
