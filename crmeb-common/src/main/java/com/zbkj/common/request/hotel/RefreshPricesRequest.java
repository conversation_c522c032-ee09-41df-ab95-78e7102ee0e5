package com.zbkj.common.request.hotel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 手动刷新房型价格请求类
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Data
@ApiModel(value = "RefreshPricesRequest", description = "手动刷新房型价格请求")
public class RefreshPricesRequest {

    @ApiModelProperty(value = "房型ID列表，为空时刷新当前商户所有房型", required = false)
    private List<Integer> roomIds;

    @ApiModelProperty(value = "是否异步执行，默认false", required = false)
    private Boolean async = false;
}
