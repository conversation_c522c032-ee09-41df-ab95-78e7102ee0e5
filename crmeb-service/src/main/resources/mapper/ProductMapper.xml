<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zbkj.service.dao.ProductDao">

    <!-- 批量更新商品信息 - 优化版本 -->
    <update id="batchUpdateProducts" parameterType="java.util.List">
        <if test="products != null and products.size() > 0">
            UPDATE eb_product
            <set>
                name = CASE id
                    <foreach collection="products" item="item">
                        WHEN #{item.id} THEN #{item.name}
                    </foreach>
                    ELSE name
                END,
                keyword = CASE id
                    <foreach collection="products" item="item">
                        WHEN #{item.id} THEN #{item.keyword}
                    </foreach>
                    ELSE keyword
                END,
                price = CASE id
                    <foreach collection="products" item="item">
                        WHEN #{item.id} THEN #{item.price}
                    </foreach>
                    ELSE price
                END,
                ot_price = CASE id
                    <foreach collection="products" item="item">
                        WHEN #{item.id} THEN #{item.otPrice}
                    </foreach>
                    ELSE ot_price
                END,
                cost = CASE id
                    <foreach collection="products" item="item">
                        WHEN #{item.id} THEN #{item.cost}
                    </foreach>
                    ELSE cost
                END,
                stock = CASE id
                    <foreach collection="products" item="item">
                        WHEN #{item.id} THEN #{item.stock}
                    </foreach>
                    ELSE stock
                END,
                update_time = NOW()
            </set>
            WHERE id IN
            <foreach collection="products" item="item" open="(" separator="," close=")">
                #{item.id}
            </foreach>
        </if>
    </update>

    <!-- 批量更新商品价格（仅价格相关字段） -->
    <update id="batchUpdateProductPrices" parameterType="java.util.List">
        <if test="products != null and products.size() > 0">
            UPDATE eb_product
            <set>
                price = CASE id
                    <foreach collection="products" item="item">
                        WHEN #{item.id} THEN #{item.price}
                    </foreach>
                    ELSE price
                END,
                ot_price = CASE id
                    <foreach collection="products" item="item">
                        WHEN #{item.id} THEN #{item.otPrice}
                    </foreach>
                    ELSE ot_price
                END,
                cost = CASE id
                    <foreach collection="products" item="item">
                        WHEN #{item.id} THEN #{item.cost}
                    </foreach>
                    ELSE cost
                END,
                update_time = NOW()
            </set>
            WHERE id IN
            <foreach collection="products" item="item" open="(" separator="," close=")">
                #{item.id}
            </foreach>
        </if>
    </update>

</mapper>
