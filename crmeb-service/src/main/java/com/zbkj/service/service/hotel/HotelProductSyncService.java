package com.zbkj.service.service.hotel;

import java.time.LocalDate;
import java.util.List;

/**
 * 酒店商品同步服务接口
 *
 * 功能说明：
 * 1. 负责将酒店房间信息同步到商品表
 * 2. 处理价格策略计算和商品生成
 * 3. 管理商品分类和图片处理
 * 4. 支持定时任务和手动同步
 * 5. 支持实时同步，确保特价状态一致性
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
public interface HotelProductSyncService {

    /**
     * 同步所有酒店商品
     * 遍历所有启用的酒店房间，生成对应的商品
     */
    void syncAllHotelProducts();

    /**
     * 同步指定商户的酒店商品
     * 
     * @param merchantId 商户ID
     */
    void syncHotelProductsByMerchant(Integer merchantId);

    /**
     * 同步指定房间的商品
     * 
     * @param roomId 房间ID
     */
    void syncHotelProductsByRoom(Integer roomId);

    /**
     * 清理过期的酒店商品
     * 删除超过指定天数且无订单关联的过期商品
     */
    void cleanExpiredHotelProducts();



    /**
     * 批量更新酒店商品价格
     * 当价格策略变更时，更新相关商品价格
     *
     * @param roomId 房间ID
     */
    void updateHotelProductPrices(Integer roomId);

    // ==================== 实时同步方法 ====================

    /**
     * 实时同步指定房型的所有商品
     * 立即更新商品价格、名称标记和关键词，确保特价状态一致性
     *
     * @param roomId 房型ID
     */
    void syncRoomProductsRealTime(Integer roomId);

    /**
     * 实时同步指定房型的日期范围商品
     *
     * @param roomId 房型ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    void syncRoomProductsByDateRange(Integer roomId, LocalDate startDate, LocalDate endDate);

    /**
     * 批量实时同步多个房型
     *
     * @param roomIds 房型ID列表
     */
    void syncMultipleRoomsRealTime(List<Integer> roomIds);

    /**
     * 手动触发单个商户的所有房型同步
     *
     * @param merchantId 商户ID
     */
    void syncMerchantRoomsRealTime(Integer merchantId);

}
