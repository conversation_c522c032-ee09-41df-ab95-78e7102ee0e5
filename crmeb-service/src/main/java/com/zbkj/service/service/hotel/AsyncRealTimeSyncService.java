package com.zbkj.service.service.hotel;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 异步实时同步服务
 * 提供异步执行的实时同步功能，避免阻塞主线程
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Slf4j
@Service
public class AsyncRealTimeSyncService {

    @Autowired
    private HotelProductSyncService hotelProductSyncService;

    @Autowired
    private RealTimeSyncMonitor realTimeSyncMonitor;

    /**
     * 异步同步房型商品
     * 
     * @param roomId 房型ID
     * @return CompletableFuture
     */
    @Async("hotelSyncExecutor")
    public CompletableFuture<Void> syncRoomProductsAsync(Integer roomId) {
        String monitorId = realTimeSyncMonitor.recordSyncStart("async_room", roomId.toString());
        
        try {
            log.info("🔄 异步同步房型 {} 开始", roomId);
            hotelProductSyncService.syncRoomProductsRealTime(roomId);
            
            realTimeSyncMonitor.recordSyncEnd(monitorId, true, 1, null);
            log.info("✅ 异步同步房型 {} 完成", roomId);
            
        } catch (Exception e) {
            realTimeSyncMonitor.recordSyncEnd(monitorId, false, 0, e.getMessage());
            log.error("❌ 异步同步房型 {} 失败", roomId, e);
            throw e;
        }
        
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 异步批量同步多个房型
     * 
     * @param roomIds 房型ID列表
     * @return CompletableFuture
     */
    @Async("hotelSyncExecutor")
    public CompletableFuture<Void> syncMultipleRoomsAsync(List<Integer> roomIds) {
        String monitorId = realTimeSyncMonitor.recordSyncStart("async_batch", "rooms_" + roomIds.size());
        
        try {
            log.info("🔄 异步批量同步房型开始，数量: {}", roomIds.size());
            
            // 并行处理多个房型
            List<CompletableFuture<Void>> futures = roomIds.stream()
                    .map(this::syncRoomProductsAsync)
                    .toList();
            
            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            
            realTimeSyncMonitor.recordSyncEnd(monitorId, true, roomIds.size(), null);
            log.info("✅ 异步批量同步房型完成，数量: {}", roomIds.size());
            
        } catch (Exception e) {
            realTimeSyncMonitor.recordSyncEnd(monitorId, false, 0, e.getMessage());
            log.error("❌ 异步批量同步房型失败，数量: {}", roomIds.size(), e);
            throw e;
        }
        
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 异步同步商户所有房型
     * 
     * @param merchantId 商户ID
     * @return CompletableFuture
     */
    @Async("hotelSyncExecutor")
    public CompletableFuture<Void> syncMerchantRoomsAsync(Integer merchantId) {
        String monitorId = realTimeSyncMonitor.recordSyncStart("async_merchant", merchantId.toString());
        
        try {
            log.info("🔄 异步同步商户 {} 所有房型开始", merchantId);
            hotelProductSyncService.syncMerchantRoomsRealTime(merchantId);
            
            realTimeSyncMonitor.recordSyncEnd(monitorId, true, 1, null);
            log.info("✅ 异步同步商户 {} 所有房型完成", merchantId);
            
        } catch (Exception e) {
            realTimeSyncMonitor.recordSyncEnd(monitorId, false, 0, e.getMessage());
            log.error("❌ 异步同步商户 {} 所有房型失败", merchantId, e);
            throw e;
        }
        
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 异步同步房型指定日期范围
     * 
     * @param roomId 房型ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return CompletableFuture
     */
    @Async("hotelSyncExecutor")
    public CompletableFuture<Void> syncRoomProductsByDateRangeAsync(Integer roomId, LocalDate startDate, LocalDate endDate) {
        String monitorId = realTimeSyncMonitor.recordSyncStart("async_date_range", 
                roomId + "_" + startDate + "_" + endDate);
        
        try {
            log.info("🔄 异步同步房型 {} 日期范围 {} 到 {} 开始", roomId, startDate, endDate);
            hotelProductSyncService.syncRoomProductsByDateRange(roomId, startDate, endDate);
            
            realTimeSyncMonitor.recordSyncEnd(monitorId, true, 1, null);
            log.info("✅ 异步同步房型 {} 日期范围完成", roomId);
            
        } catch (Exception e) {
            realTimeSyncMonitor.recordSyncEnd(monitorId, false, 0, e.getMessage());
            log.error("❌ 异步同步房型 {} 日期范围失败", roomId, e);
            throw e;
        }
        
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 检查异步任务状态
     * 
     * @param future CompletableFuture对象
     * @return 任务状态
     */
    public String getAsyncTaskStatus(CompletableFuture<Void> future) {
        if (future.isDone()) {
            if (future.isCompletedExceptionally()) {
                return "FAILED";
            } else {
                return "COMPLETED";
            }
        } else {
            return "RUNNING";
        }
    }

    /**
     * 取消异步任务
     * 
     * @param future CompletableFuture对象
     * @return 是否成功取消
     */
    public boolean cancelAsyncTask(CompletableFuture<Void> future) {
        if (future != null && !future.isDone()) {
            boolean cancelled = future.cancel(true);
            log.info("异步任务取消结果: {}", cancelled ? "成功" : "失败");
            return cancelled;
        }
        return false;
    }
}
