package com.zbkj.service.service.hotel;

import com.zbkj.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 实时同步监控服务
 * 记录同步操作的统计信息和性能指标
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Slf4j
@Component
public class RealTimeSyncMonitor {

    @Autowired
    private RedisUtil redisUtil;

    private static final String MONITOR_KEY_PREFIX = "sync_monitor:";
    private static final String DAILY_STATS_KEY = "daily_stats:";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 记录同步开始
     * 
     * @param syncType 同步类型（room/merchant/batch）
     * @param targetId 目标ID
     * @return 监控ID，用于记录结束时间
     */
    public String recordSyncStart(String syncType, String targetId) {
        String monitorId = generateMonitorId(syncType, targetId);
        String startTime = LocalDateTime.now().toString();
        
        // 记录开始时间
        redisUtil.set(MONITOR_KEY_PREFIX + monitorId + ":start", startTime, 3600); // 1小时过期
        
        // 增加今日同步次数统计
        String todayKey = DAILY_STATS_KEY + LocalDateTime.now().format(DATE_FORMATTER) + ":" + syncType;
        redisUtil.incr(todayKey, 1);
        redisUtil.expire(todayKey, 86400 * 7); // 保留7天
        
        log.info("🚀 实时同步开始 - 类型: {}, 目标: {}, 监控ID: {}", syncType, targetId, monitorId);
        return monitorId;
    }

    /**
     * 记录同步结束
     * 
     * @param monitorId 监控ID
     * @param success 是否成功
     * @param updatedCount 更新数量
     * @param errorMessage 错误信息（如果失败）
     */
    public void recordSyncEnd(String monitorId, boolean success, int updatedCount, String errorMessage) {
        try {
            String startTimeStr = (String) redisUtil.get(MONITOR_KEY_PREFIX + monitorId + ":start");
            if (startTimeStr == null) {
                log.warn("未找到同步开始时间，监控ID: {}", monitorId);
                return;
            }
            
            LocalDateTime startTime = LocalDateTime.parse(startTimeStr);
            LocalDateTime endTime = LocalDateTime.now();
            long durationMs = java.time.Duration.between(startTime, endTime).toMillis();
            
            // 记录性能指标
            Map<String, Object> metrics = new HashMap<>();
            metrics.put("startTime", startTimeStr);
            metrics.put("endTime", endTime.toString());
            metrics.put("durationMs", durationMs);
            metrics.put("success", success);
            metrics.put("updatedCount", updatedCount);
            if (!success && errorMessage != null) {
                metrics.put("errorMessage", errorMessage);
            }
            
            // 存储详细监控数据（保留1小时）
            redisUtil.set(MONITOR_KEY_PREFIX + monitorId + ":result", metrics, 3600);
            
            // 更新性能统计
            updatePerformanceStats(durationMs, success, updatedCount);
            
            // 清理开始时间记录
            redisUtil.del(MONITOR_KEY_PREFIX + monitorId + ":start");
            
            if (success) {
                log.info("✅ 实时同步完成 - 监控ID: {}, 耗时: {}ms, 更新数量: {}", 
                        monitorId, durationMs, updatedCount);
            } else {
                log.error("❌ 实时同步失败 - 监控ID: {}, 耗时: {}ms, 错误: {}", 
                        monitorId, durationMs, errorMessage);
            }
            
        } catch (Exception e) {
            log.error("记录同步结束信息失败，监控ID: {}", monitorId, e);
        }
    }

    /**
     * 更新性能统计
     */
    private void updatePerformanceStats(long durationMs, boolean success, int updatedCount) {
        String today = LocalDateTime.now().format(DATE_FORMATTER);
        
        try {
            // 总耗时统计
            String totalTimeKey = "perf_stats:" + today + ":total_time";
            redisUtil.incr(totalTimeKey, durationMs);
            redisUtil.expire(totalTimeKey, 86400 * 7);
            
            // 成功/失败次数统计
            String resultKey = "perf_stats:" + today + ":" + (success ? "success" : "failed");
            redisUtil.incr(resultKey, 1);
            redisUtil.expire(resultKey, 86400 * 7);
            
            // 更新商品数量统计
            if (success && updatedCount > 0) {
                String updateCountKey = "perf_stats:" + today + ":updated_products";
                redisUtil.incr(updateCountKey, updatedCount);
                redisUtil.expire(updateCountKey, 86400 * 7);
            }
            
            // 记录最大/最小耗时
            updateMinMaxDuration(today, durationMs);
            
        } catch (Exception e) {
            log.error("更新性能统计失败", e);
        }
    }

    /**
     * 更新最大/最小耗时记录
     */
    private void updateMinMaxDuration(String date, long durationMs) {
        String maxKey = "perf_stats:" + date + ":max_duration";
        String minKey = "perf_stats:" + date + ":min_duration";
        
        // 更新最大耗时
        Long currentMax = (Long) redisUtil.get(maxKey);
        if (currentMax == null || durationMs > currentMax) {
            redisUtil.set(maxKey, durationMs, 86400 * 7);
        }
        
        // 更新最小耗时
        Long currentMin = (Long) redisUtil.get(minKey);
        if (currentMin == null || durationMs < currentMin) {
            redisUtil.set(minKey, durationMs, 86400 * 7);
        }
    }

    /**
     * 获取今日同步统计
     */
    public Map<String, Object> getTodayStats() {
        String today = LocalDateTime.now().format(DATE_FORMATTER);
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 同步次数统计
            stats.put("roomSyncCount", redisUtil.get(DAILY_STATS_KEY + today + ":room") != null ? 
                    redisUtil.get(DAILY_STATS_KEY + today + ":room") : 0);
            stats.put("merchantSyncCount", redisUtil.get(DAILY_STATS_KEY + today + ":merchant") != null ? 
                    redisUtil.get(DAILY_STATS_KEY + today + ":merchant") : 0);
            stats.put("batchSyncCount", redisUtil.get(DAILY_STATS_KEY + today + ":batch") != null ? 
                    redisUtil.get(DAILY_STATS_KEY + today + ":batch") : 0);
            
            // 性能统计
            stats.put("totalTime", redisUtil.get("perf_stats:" + today + ":total_time") != null ? 
                    redisUtil.get("perf_stats:" + today + ":total_time") : 0);
            stats.put("successCount", redisUtil.get("perf_stats:" + today + ":success") != null ? 
                    redisUtil.get("perf_stats:" + today + ":success") : 0);
            stats.put("failedCount", redisUtil.get("perf_stats:" + today + ":failed") != null ? 
                    redisUtil.get("perf_stats:" + today + ":failed") : 0);
            stats.put("updatedProducts", redisUtil.get("perf_stats:" + today + ":updated_products") != null ? 
                    redisUtil.get("perf_stats:" + today + ":updated_products") : 0);
            stats.put("maxDuration", redisUtil.get("perf_stats:" + today + ":max_duration") != null ? 
                    redisUtil.get("perf_stats:" + today + ":max_duration") : 0);
            stats.put("minDuration", redisUtil.get("perf_stats:" + today + ":min_duration") != null ? 
                    redisUtil.get("perf_stats:" + today + ":min_duration") : 0);
            
            // 计算平均耗时
            Long totalTime = (Long) stats.get("totalTime");
            Integer successCount = (Integer) stats.get("successCount");
            if (totalTime != null && successCount != null && successCount > 0) {
                stats.put("avgDuration", totalTime / successCount);
            } else {
                stats.put("avgDuration", 0);
            }
            
        } catch (Exception e) {
            log.error("获取今日统计失败", e);
        }
        
        return stats;
    }

    /**
     * 生成监控ID
     */
    private String generateMonitorId(String syncType, String targetId) {
        return syncType + "_" + targetId + "_" + System.currentTimeMillis();
    }

    /**
     * 记录警告信息
     */
    public void recordWarning(String syncType, String targetId, String warning) {
        log.warn("⚠️ 实时同步警告 - 类型: {}, 目标: {}, 警告: {}", syncType, targetId, warning);
        
        // 记录警告到Redis（可选）
        String warningKey = "sync_warnings:" + LocalDateTime.now().format(DATE_FORMATTER);
        Map<String, Object> warningInfo = new HashMap<>();
        warningInfo.put("time", LocalDateTime.now().toString());
        warningInfo.put("syncType", syncType);
        warningInfo.put("targetId", targetId);
        warningInfo.put("warning", warning);
        
        try {
            redisUtil.lPush(warningKey, warningInfo);
            redisUtil.expire(warningKey, 86400 * 3); // 保留3天
        } catch (Exception e) {
            log.error("记录警告信息到Redis失败", e);
        }
    }
}
