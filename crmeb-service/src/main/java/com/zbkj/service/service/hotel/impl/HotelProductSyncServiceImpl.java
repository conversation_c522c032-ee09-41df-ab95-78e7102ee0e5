package com.zbkj.service.service.hotel.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zbkj.common.enums.DateTypeEnum;
import com.zbkj.common.model.hotel.HotelRoom;
import com.zbkj.common.model.hotel.HotelRoomPriceStrategy;
import com.zbkj.common.model.merchant.Merchant;
import com.zbkj.common.model.product.Product;
import com.zbkj.common.model.product.ProductAttrValue;
import com.zbkj.common.model.product.ProductDescription;
import com.zbkj.service.service.*;
import com.zbkj.service.service.hotel.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 酒店商品同步服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Slf4j
@Service
public class HotelProductSyncServiceImpl implements HotelProductSyncService {

    @Autowired
    private HotelRoomService hotelRoomService;

    @Autowired
    private HotelRoomPriceStrategyService priceStrategyService;

    @Autowired
    private ProductService productService;

    @Autowired
    private ProductAttrValueService productAttrValueService;

    @Autowired
    private ProductDescriptionService productDescriptionService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private ChineseCalendarService chineseCalendarService;

    @Autowired
    private RealTimeSyncLimiter realTimeSyncLimiter;

    @Autowired
    private RealTimeSyncMonitor realTimeSyncMonitor;

    @Autowired
    private HotelCategoryManageService categoryManageService;

    /**
     * 默认生成天数
     */
    private static final int DEFAULT_GENERATE_DAYS = 30;

    /**
     * 过期清理天数 - 保留7天历史数据，清理7天前的过期商品
     */
    private static final int EXPIRED_CLEAN_DAYS = 7;
//    private static final int EXPIRED_CLEAN_DAYS = 0;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncAllHotelProducts() {
        log.info("开始同步所有酒店商品");

        HotelProductSyncResult result = new HotelProductSyncResult()
                .setStartTime(new Date())
                .setSuccess(true);

        try {
            // 获取所有启用的酒店房间
            List<HotelRoom> hotelRooms = hotelRoomService.getAllActiveRooms();

            if (CollUtil.isEmpty(hotelRooms)) {
                log.info("没有找到启用的酒店房间，同步结束");
                return;
            }

            result.setTotalRooms(hotelRooms.size());

            int successCount = 0;
            int failedCount = 0;
            int newProducts = 0;
            int updatedProducts = 0;
            int skippedProducts = 0;

            // 按商户分组处理
         Map<Integer, List<HotelRoom>> merchantGroups = hotelRooms.stream()
                    .collect(Collectors.groupingBy(HotelRoom::getMerId));

            result.setTotalMerchants(merchantGroups.size());

            for (Map.Entry<Integer, List<HotelRoom>> entry : merchantGroups.entrySet()) {
                Integer merchantId = entry.getKey();
                List<HotelRoom> merchantRooms = entry.getValue();

                log.info("开始处理商户 {} 的 {} 个房间", merchantId, merchantRooms.size());

                // 确保商户分类存在
                categoryManageService.ensureMerchantCategory(merchantId);

                for (HotelRoom room : merchantRooms) {
                    try {
                        HotelProductSyncResult roomResult = syncSingleRoom(room);
                        successCount++;
                        newProducts += roomResult.getNewProducts();
                        updatedProducts += roomResult.getUpdatedProducts();
                        skippedProducts += roomResult.getSkippedProducts();

                    } catch (Exception e) {
                        log.error("同步房间失败，房间ID: {}, 错误: {}", room.getId(), e.getMessage(), e);
                        failedCount++;
                    }
                }
            }

            result.setSuccessRooms(successCount)
                  .setFailedRooms(failedCount)
                  .setNewProducts(newProducts)
                  .setUpdatedProducts(updatedProducts)
                  .setSkippedProducts(skippedProducts)
                  .setEndTime(new Date());

            log.info("酒店商品同步完成，统计: {}", JSON.toJSONString(result));

        } catch (Exception e) {
            log.error("同步所有酒店商品失败", e);
            result.setSuccess(false).setErrorMessage(e.getMessage());
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncHotelProductsByMerchant(Integer merchantId) {
        log.info("开始同步商户 {} 的酒店商品", merchantId);

        List<HotelRoom> rooms = hotelRoomService.getActiveRoomsByMerchant(merchantId);

        if (CollUtil.isEmpty(rooms)) {
            log.info("商户 {} 没有启用的酒店房间", merchantId);
            return;
        }

        // 确保商户分类存在
        categoryManageService.ensureMerchantCategory(merchantId);

        for (HotelRoom room : rooms) {
            try {
                syncSingleRoom(room);
                log.debug("房间 {} 同步完成", room.getId());
            } catch (Exception e) {
                log.error("同步房间失败，房间ID: {}, 错误: {}", room.getId(), e.getMessage(), e);
            }
        }

        log.info("商户 {} 的酒店商品同步完成", merchantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
            @CacheEvict(value = "hotel:room:details", allEntries = true),
            @CacheEvict(value = "hotel:room:list", allEntries = true),
            @CacheEvict(value = "hotel:list", allEntries = true)
    })
    public void syncHotelProductsByRoom(Integer roomId) {
        log.info("开始同步房间 {} 的商品", roomId);

        HotelRoom room = hotelRoomService.getById(roomId);
        if (room == null || room.getStatus() != 1) {
            log.warn("房间 {} 不存在或未启用", roomId);
            return;
        }

        // 确保商户分类存在
        categoryManageService.ensureMerchantCategory(room.getMerId());

        syncSingleRoom(room);

        log.info("房间 {} 的商品同步完成", roomId);
    }

    /**
     * 同步单个房间的商品
     */
    private HotelProductSyncResult syncSingleRoom(HotelRoom room) {
        HotelProductSyncResult result = new HotelProductSyncResult()
                .setNewProducts(0)
                .setUpdatedProducts(0)
                .setSkippedProducts(0);

        // 获取商户信息
        Merchant merchant = merchantService.getById(room.getMerId());
        if (merchant == null) {
            throw new RuntimeException("商户不存在，ID: " + room.getMerId());
        }

        // 生成未来指定天数的商品
        LocalDate startDate = LocalDate.now().plusDays(1);
        LocalDate endDate = startDate.plusDays(room.getMaxBookingDays() != null ?
                room.getMaxBookingDays() : DEFAULT_GENERATE_DAYS);

        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            try {
                String productName = generateProductName(merchant.getName(), room.getRoomName(), date, room);

                // 检查商品是否已存在
                Product existingProduct = productService.getByName(productName);

                if (existingProduct != null) {
                    // 商品已存在，检查是否需要更新
                    if (shouldUpdateProduct(existingProduct, room, date)) {
                        updateExistingProduct(existingProduct, room, date);
                        result.setUpdatedProducts(result.getUpdatedProducts() + 1);
                    } else {
                        result.setSkippedProducts(result.getSkippedProducts() + 1);
                    }
                } else {
                    // 创建新商品
                    createNewProduct(room, merchant, date);
                    result.setNewProducts(result.getNewProducts() + 1);
                }

            } catch (Exception e) {
                log.error("处理日期 {} 的商品失败，房间ID: {}", date, room.getId(), e);
                throw e;
            }
        }

        return result;
    }

    /**
     * 创建新商品
     */
    private void createNewProduct(HotelRoom room, Merchant merchant, LocalDate checkInDate) {
        // 构建商品对象
        Product product = buildHotelProduct(room, merchant, checkInDate);

        // 保存商品
        productService.save(product);

        // 创建商品详情
        createProductDescription(product.getId(), room, checkInDate);

        // 创建商品属性值（单规格）
        createProductAttrValue(product, room, checkInDate);

        log.debug("创建酒店商品成功: {}", product.getName());
    }

    /**
     * 更新现有商品
     */
    private void updateExistingProduct(Product product, HotelRoom room, LocalDate checkInDate) {
        // 获取商户信息（用于重新生成商品名称）
        Merchant merchant = merchantService.getById(room.getMerId());

        // 🔄 重新生成商品名称（包含最新标记）
        String newProductName = generateProductName(merchant.getName(), room.getRoomName(), checkInDate, room);
        product.setName(newProductName);

        // 🔄 重新生成关键词（包含最新标记）
        String newKeywords = generateProductKeywords(room, checkInDate);
        product.setKeyword(newKeywords);

        // 更新价格
        BigDecimal newPrice = calculatePrice(room.getId(), checkInDate);
        product.setPrice(newPrice);
        product.setOtPrice(newPrice.multiply(new BigDecimal("1.2")));
        product.setCost(newPrice.multiply(new BigDecimal("0.8")));

        // 更新库存
        product.setStock(room.getTotalRooms());

        // 更新图片
        if (StrUtil.isNotBlank(room.getRoomImages())) {
            product.setImage(processRoomImage(room.getRoomImages()));
            product.setSliderImage(room.getRoomImages());
            product.setFlatPattern(room.getRoomImages());
        }

        productService.updateById(product);

        // 更新商品属性值
        updateProductAttrValue(product.getId(), newPrice, room.getTotalRooms());

        log.debug("更新酒店商品成功: {}", product.getName());
    }

    /**
     * 判断是否需要更新商品
     */
    private boolean shouldUpdateProduct(Product product, HotelRoom room, LocalDate checkInDate) {
        // 检查价格是否变化
        BigDecimal currentPrice = calculatePrice(room.getId(), checkInDate);
        if (product.getPrice().compareTo(currentPrice) != 0) {
            return true;
        }

        // 检查库存是否变化
        if (!product.getStock().equals(room.getTotalRooms())) {
            return true;
        }

        // 检查图片是否变化
        if (StrUtil.isNotBlank(room.getRoomImages())) {
            String newImage = processRoomImage(room.getRoomImages());
            if (!newImage.equals(product.getImage())) {
                return true;
            }
        }

        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cleanExpiredHotelProducts() {
        log.info("开始清理过期的酒店商品，保留{}天历史数据", EXPIRED_CLEAN_DAYS);

        LocalDate expiredDate = LocalDate.now().minusDays(EXPIRED_CLEAN_DAYS);
        log.info("清理基准日期: {}，将清理此日期之前的商品", expiredDate);

        // 查找酒店商品（使用专门的酒店商品类型）
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getType, 7) // 酒店房间商品
               .eq(Product::getIsDel, false);

        List<Product> products = productService.list(wrapper);

        log.info("找到 {} 个酒店商品待检查清理", products.size());

        // 打印所有找到的商品名称
        for (Product p : products) {
            log.info("待检查商品: ID={}, 名称={}", p.getId(), p.getName());
        }

        int deletedCount = 0;

        for (Product product : products) {
            try {
                // 从商品名称中提取日期
                LocalDate productDate = extractDateFromProductName(product.getName());
                log.debug("检查商品: {}, 提取日期: {}, 基准日期: {}", product.getName(), productDate, expiredDate);

                if (productDate != null && productDate.isBefore(expiredDate)) {
                    log.info("商品 {} 日期 {} 早于基准日期 {}，检查是否有关联订单", product.getName(), productDate, expiredDate);
                    // 检查是否有关联订单
                    if (!productService.hasRelatedOrders(product.getId())) {
                        // 使用MyBatis-Plus标准删除方法，自动处理逻辑删除
                        boolean deleteResult = productService.removeById(product.getId());
                        if (deleteResult) {
                            deletedCount++;
                            log.info("✅ 删除过期酒店商品: {}", product.getName());
                        } else {
                            log.warn("❌ 删除商品失败: {}", product.getName());
                        }
                    } else {
                        log.info("❌ 商品 {} 有关联订单，跳过删除", product.getName());
                    }
                } else if (productDate != null) {
                    log.debug("商品 {} 日期 {} 不早于基准日期 {}，保留", product.getName(), productDate, expiredDate);
                }

            } catch (Exception e) {
                log.error("处理过期商品失败，商品ID: {}", product.getId(), e);
            }
        }

        log.info("清理过期酒店商品完成，删除数量: {}", deletedCount);
    }

    @Override
    @Caching(evict = {
            @CacheEvict(value = "hotel:room:details", allEntries = true),
            @CacheEvict(value = "hotel:room:list", allEntries = true),
            @CacheEvict(value = "hotel:list", allEntries = true)
    })
    public void updateHotelProductPrices(Integer roomId) {
        log.info("开始更新房间 {} 的商品价格", roomId);

        HotelRoom room = hotelRoomService.getById(roomId);
        if (room == null) {
            log.warn("房间 {} 不存在", roomId);
            return;
        }

        // 查找该房间的所有商品
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(Product::getName, room.getRoomName())
               .eq(Product::getMerId, room.getMerId())
               .eq(Product::getType, 7) // 酒店房间商品
               .eq(Product::getIsDel, false);

        List<Product> products = productService.list(wrapper);

        for (Product product : products) {
            try {
                // 从商品名称提取日期
                LocalDate productDate = extractDateFromProductName(product.getName());
                if (productDate != null) {
                    // 重新计算价格
                    BigDecimal newPrice = calculatePrice(roomId, productDate);

                    product.setPrice(newPrice);
                    product.setOtPrice(newPrice.multiply(new BigDecimal("1.2")));
                    product.setCost(newPrice.multiply(new BigDecimal("0.8")));

                    productService.updateById(product);

                    // 更新商品属性值价格
                    updateProductAttrValuePrice(product.getId(), newPrice);
                }

            } catch (Exception e) {
                log.error("更新商品价格失败，商品ID: {}", product.getId(), e);
            }
        }

        log.info("房间 {} 的商品价格更新完成，更新商品数: {}", roomId, products.size());
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 生成商品名称（支持特价和周末不加价标记）
     */
    private String generateProductName(String hotelName, String roomName, LocalDate checkInDate, HotelRoom room) {
        StringBuilder name = new StringBuilder();
        name.append(String.format("%s-%s-%s", hotelName, roomName,
                    checkInDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));

        // 特价房源标记（只有实际有特价策略时才显示）
        if (room.getIsSpecialOffer() != null && room.getIsSpecialOffer() == 1) {
            if (hasActiveSpecialPrice(room.getId(), checkInDate)) {
                name.append("[特价]");
            }
        }

        // 周末不加价标记
        if (room.getWeekendNoMarkup() != null && room.getWeekendNoMarkup() == 1) {
            DateTypeEnum dateType = chineseCalendarService.getDateType(checkInDate);
            if (dateType == DateTypeEnum.WEEKEND) {
                name.append("[周末不加价]");
            }
        }

        return name.toString();
    }

    /**
     * 生成商品关键词（支持特价和周末不加价标记）
     */
    private String generateProductKeywords(HotelRoom room, LocalDate date) {
        List<String> keywords = new ArrayList<>();
        keywords.add("酒店");
        keywords.add("住宿");
        keywords.add("预订");
        keywords.add(room.getRoomName());

        // 特价房源标记（只有实际有特价策略时才添加）
        if (room.getIsSpecialOffer() != null && room.getIsSpecialOffer() == 1) {
            if (hasActiveSpecialPrice(room.getId(), date)) {
                keywords.add("special_offer");
            }
        }

        // 周末不加价标记
        if (room.getWeekendNoMarkup() != null && room.getWeekendNoMarkup() == 1) {
            keywords.add("weekend_no_markup");
        }

        return String.join(",", keywords);
    }

    /**
     * 构建酒店商品对象
     */
    private Product buildHotelProduct(HotelRoom room, Merchant merchant, LocalDate checkInDate) {
        Product product = new Product();

        // 基础信息
        product.setMerId(room.getMerId());
        product.setName(generateProductName(merchant.getName(), room.getRoomName(), checkInDate, room));
        product.setIntro(generateProductIntro(room, checkInDate));
        product.setKeyword(generateProductKeywords(room, checkInDate));

        // 分类设置 (关键!)
        Integer merchantCategoryId = categoryManageService.ensureMerchantCategory(room.getMerId());
        Integer platformCategoryId = categoryManageService.ensurePlatformCategory();

        product.setCateId(String.valueOf(merchantCategoryId)); // varchar类型
        product.setCategoryId(platformCategoryId); // int类型

        // 图片设置
        product.setImage(processRoomImage(room.getRoomImages()));
        product.setSliderImage(room.getRoomImages() != null ? room.getRoomImages() : "[]");
        product.setFlatPattern(room.getRoomImages() != null ? room.getRoomImages() : "[]");

        // 价格和库存
        BigDecimal price = calculatePrice(room.getId(), checkInDate);
        product.setPrice(price);
        product.setOtPrice(price.multiply(new BigDecimal("1.2"))); // 市场价
        product.setCost(price.multiply(new BigDecimal("0.8"))); // 成本价
        product.setVipPrice(BigDecimal.ZERO);
        product.setStock(room.getTotalRooms());

        // 商品属性
        product.setUnitName("间/晚");
        product.setType(7); // 酒店房间商品
        product.setSpecType(false); // 单规格
        product.setDeliveryMethod("2"); // 到店核销

        // 分销设置
        product.setIsSub(true); // 参与分销

        // 状态设置
        product.setIsShow(true);
        product.setIsDel(false);
        product.setAuditStatus(2); // 审核通过
        product.setIsAudit(false);
        product.setIsRecycle(false);

        // 设置默认值
        setProductDefaultValues(product);

        return product;
    }

    /**
     * 设置商品默认值
     */
    private void setProductDefaultValues(Product product) {
        product.setBrandId(0);
        product.setGuaranteeIds("0");
        product.setSales(0);
        product.setFicti(0);
        product.setBrowse(0);
        product.setTempId(1);
        product.setSort(0);
        product.setRank(0);
        product.setIsPaidMember(false);
        product.setIsAutoUp(false);
        product.setMarketingType(0);
        product.setRefundSwitch(true);
        product.setSystemFormId(0);
        product.setRedeemIntegral(0);
        product.setExchangeNum(0);
        product.setIsHot(0);
    }

    /**
     * 生成商品简介
     */
    private String generateProductIntro(HotelRoom room, LocalDate checkInDate) {
        return String.format("入住日期：%s，房型：%s，到店核销",
                           checkInDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                           room.getRoomName());
    }

    /**
     * 处理房间图片
     */
    private String processRoomImage(String roomImagesJson) {
        if (StrUtil.isNotBlank(roomImagesJson)) {
            try {
                List<String> images = JSON.parseArray(roomImagesJson, String.class);
                return CollUtil.isNotEmpty(images) ? images.get(0) : getDefaultHotelImage();
            } catch (Exception e) {
                log.warn("解析房间图片失败: {}", roomImagesJson, e);
            }
        }
        return getDefaultHotelImage();
    }

    /**
     * 获取默认酒店图片
     */
    private String getDefaultHotelImage() {
        return "/static/images/hotel_default.jpg";
    }

    /**
     * 🎯 酒店房间价格计算核心引擎（支持特价策略和周末不加价）
     *
     * 【业务逻辑】
     * 根据指定房间ID和入住日期，通过多层策略匹配机制计算最终房间价格
     *
     * 【计算流程】
     * 1. 获取房型信息：查询房型的特价和周末不加价设置
     * 2. 日期类型识别：调用中国日历API，识别日期类型（工作日/周末/节假日/调休补班）
     * 3. 策略集合获取：查询该房间的所有启用价格策略
     * 4. 特价策略优先：如果房型支持特价且有启用的特价策略，优先使用
     * 5. 周末不加价处理：如果房型设置周末不加价且为周末，使用工作日价格
     * 6. 普通策略匹配：按原有逻辑进行策略匹配
     *
     * 【策略类型优先级】
     * - 特价策略（strategy_type=6）：最高优先级
     * - 周末不加价：中等优先级（周末时使用工作日价格）
     * - 节假日价格策略（priority=3）：法定节假日适用
     * - 周末价格策略（priority=2）：周六周日适用
     * - 基础价格策略（priority=1）：工作日和调休补班适用
     *
     * 【异常处理】
     * - 无策略配置：返回0元，记录警告日志
     * - 计算异常：返回0元，记录错误日志
     *
     * @param roomId 房间ID，用于查询该房间的价格策略配置
     * @param date 入住日期，用于确定日期类型和匹配相应价格策略
     * @return 计算得出的房间价格，单位：元（精确到分）
     */
    private BigDecimal calculatePrice(Integer roomId, LocalDate date) {
        try {
            // 🏨 步骤1：获取房型信息（新增）
            HotelRoom room = hotelRoomService.getById(roomId);
            if (room == null) {
                log.warn("房间 {} 不存在", roomId);
                return BigDecimal.ZERO;
            }

            // 🗓️ 步骤2：调用中国日历服务，获取日期类型（集成第三方节假日API）
            DateTypeEnum dateType = chineseCalendarService.getDateType(date);
            log.debug("房间 {} 日期 {} 识别为：{}", roomId, date, dateType.getDesc());

            // 📋 步骤3：查询房间的所有启用价格策略（按优先级和创建时间排序）
            List<HotelRoomPriceStrategy> strategies = priceStrategyService.getByRoomId(roomId);

            if (CollUtil.isEmpty(strategies)) {
                log.warn("房间 {} 没有配置价格策略，返回0元", roomId);
                return BigDecimal.ZERO;
            }

            // 🎯 步骤4：特价策略处理（动态价格策略）
            if (room.getIsSpecialOffer() != null && room.getIsSpecialOffer() == 1) {
                // 查找启用的特价策略（strategy_type = 6）
                BigDecimal specialPrice = findSpecialOfferPrice(strategies, dateType, date);
                if (specialPrice != null && specialPrice.compareTo(BigDecimal.ZERO) > 0) {
                    log.info("房间 {} 日期 {} 使用特价策略，价格：{}", roomId, date, specialPrice);
                    return specialPrice;
                }
            }

            // 🎯 步骤5：周末不加价处理（房型属性判断）
            if (room.getWeekendNoMarkup() != null && room.getWeekendNoMarkup() == 1) {
                if (dateType == DateTypeEnum.WEEKEND) {
                    // 周末不加价，强制使用工作日价格
                    BigDecimal workdayPrice = findWorkdayPrice(strategies);
                    if (workdayPrice != null && workdayPrice.compareTo(BigDecimal.ZERO) > 0) {
                        log.info("房间 {} 日期 {} 启用周末不加价，使用工作日价格：{}", roomId, date, workdayPrice);
                        return workdayPrice;
                    }
                }
            }

            // 🎯 步骤6：原有策略匹配逻辑
            // 过滤出匹配当前日期类型的策略，然后按优先级降序排序，取最高优先级的价格
            return strategies.stream()
                .filter(s -> matchesDateType(s, dateType, date))  // 策略匹配过滤
                .max(Comparator.comparing(HotelRoomPriceStrategy::getPriority))  // 优先级排序
                .map(HotelRoomPriceStrategy::getPriceValue)  // 提取价格值
                .orElse(BigDecimal.ZERO);  // 无匹配策略时返回0元

        } catch (Exception e) {
            log.error("计算价格失败，房间ID: {}, 日期: {}", roomId, date, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 查找特价策略价格
     */
    private BigDecimal findSpecialOfferPrice(List<HotelRoomPriceStrategy> strategies, DateTypeEnum dateType, LocalDate date) {
        return strategies.stream()
            .filter(s -> s.getStrategyType() == 6) // 特价策略类型
            .filter(s -> s.getStatus() == 1) // 启用状态
            .filter(s -> matchesDateType(s, dateType, date)) // 匹配日期类型
            .max(Comparator.comparing(HotelRoomPriceStrategy::getPriority)) // 最高优先级
            .map(HotelRoomPriceStrategy::getPriceValue)
            .orElse(null);
    }

    /**
     * 查找工作日价格
     */
    private BigDecimal findWorkdayPrice(List<HotelRoomPriceStrategy> strategies) {
        return strategies.stream()
            .filter(s -> s.getStrategyType() == 1) // 工作日策略类型
            .filter(s -> s.getStatus() == 1) // 启用状态
            .max(Comparator.comparing(HotelRoomPriceStrategy::getPriority))
            .map(HotelRoomPriceStrategy::getPriceValue)
            .orElse(null);
    }

    /**
     * 检查是否有生效的特价策略
     */
    private boolean hasActiveSpecialPrice(Integer roomId, LocalDate date) {
        List<HotelRoomPriceStrategy> strategies = priceStrategyService.getByRoomId(roomId);
        DateTypeEnum dateType = chineseCalendarService.getDateType(date);

        return strategies.stream()
            .anyMatch(s -> s.getStrategyType() == 6 // 特价策略类型
                        && s.getStatus() == 1 // 启用状态
                        && matchesDateType(s, dateType, date)); // 匹配日期类型
    }

    /**
     * 🎯 价格策略匹配引擎
     *
     * 【核心功能】
     * 判断指定的价格策略是否适用于给定的日期类型和目标日期
     *
     * 【匹配规则】
     * 1. 基础价格策略（strategyType=1）：匹配工作日和调休补班日
     * 2. 周末价格策略（strategyType=2）：匹配自然周末（周六、周日）
     * 3. 节假日价格策略（strategyType=3）：匹配法定节假日
     * 4. 日期范围策略（strategyType=4）：匹配指定日期范围内的所有日期
     * 5. 具体日期策略（strategyType=5）：匹配JSON配置的特定日期列表
     *
     * 【优先级说明】
     * - 具体日期策略 > 日期范围策略 > 节假日策略 > 周末策略 > 基础价格策略
     * - 系统会自动选择优先级最高的匹配策略
     *
     * @param strategy 价格策略对象，包含策略类型、日期范围、具体日期等配置
     * @param dateType 日期类型枚举，由中国日历服务识别得出
     * @param targetDate 目标日期，用于日期范围和具体日期的精确匹配
     * @return true-策略匹配成功，false-策略不匹配
     */
    private boolean matchesDateType(HotelRoomPriceStrategy strategy, DateTypeEnum dateType, LocalDate targetDate) {
        switch (strategy.getStrategyType()) {
            case 1: // 基础价格策略：工作日（周一至周五）+ 调休补班日
                return dateType == DateTypeEnum.WORKDAY || dateType == DateTypeEnum.TRANSFER_WORKDAY;

            case 2: // 周末价格策略：自然周末（周六、周日，不包含调休）
                return dateType == DateTypeEnum.WEEKEND;

            case 3: // 节假日价格策略：法定节假日（春节、国庆等）
                return dateType == DateTypeEnum.HOLIDAY;

            case 4: // 日期范围策略：指定开始和结束日期的连续范围
                return matchesDateRange(strategy, targetDate);

            case 5: // 具体日期策略：JSON配置的特定日期列表（如促销日、特殊活动日）
                return matchesSpecificDates(strategy, targetDate);

            default:
                log.warn("未知的策略类型: {}", strategy.getStrategyType());
                return false;
        }
    }

    /**
     * 📅 日期范围匹配器 暂定(待使用该方案)
     *
     * 【功能说明】
     * 判断目标日期是否在策略配置的日期范围内（包含起始和结束日期）
     *
     * 【应用场景】
     * - 旺季价格策略：如暑假期间（7月1日-8月31日）
     * - 淡季价格策略：如冬季期间（12月1日-2月28日）
     * - 活动价格策略：如周年庆期间（特定日期范围）
     *
     * 【匹配逻辑】
     * 使用闭区间匹配：startDate ≤ targetDate ≤ endDate
     *
     * @param strategy 包含startDate和endDate的价格策略
     * @param targetDate 需要匹配的目标日期
     * @return true-目标日期在范围内，false-不在范围内或参数无效
     */
    private boolean matchesDateRange(HotelRoomPriceStrategy strategy, LocalDate targetDate) {
        // 参数有效性检查
        if (strategy.getStartDate() == null || strategy.getEndDate() == null || targetDate == null) {
            log.debug("日期范围匹配失败：参数无效，startDate={}, endDate={}, targetDate={}",
                     strategy.getStartDate(), strategy.getEndDate(), targetDate);
            return false;
        }

        // 转换Date为LocalDate进行比较
        LocalDate startDate = dateToLocalDate(strategy.getStartDate());
        LocalDate endDate = dateToLocalDate(strategy.getEndDate());

        // 闭区间匹配：[startDate, endDate]
        boolean matches = !targetDate.isBefore(startDate) && !targetDate.isAfter(endDate);

        log.debug("日期范围匹配结果：{} 在 [{}, {}] 范围内 = {}",
                 targetDate, startDate, endDate, matches);

        return matches;
    }

    /**
     * 📋 具体日期匹配器 暂定(待使用该方案)
     *
     * 【功能说明】
     * 判断目标日期是否在策略配置的具体日期列表中
     *
     * 【应用场景】
     * - 特殊节日价格：如情人节、母亲节等非法定节假日
     * - 促销活动日：如双11、618等电商节日
     * - 酒店纪念日：如开业周年庆等特殊日期
     *
     * 【数据格式】
     * JSON字符串数组：["2025-02-14", "2025-05-12", "2025-11-11"]
     *
     * 【匹配逻辑】
     * 将目标日期格式化为"yyyy-MM-dd"字符串，在JSON数组中查找
     *
     * @param strategy 包含specificDates（JSON格式）的价格策略
     * @param targetDate 需要匹配的目标日期
     * @return true-目标日期在列表中，false-不在列表中或解析失败
     */
    private boolean matchesSpecificDates(HotelRoomPriceStrategy strategy, LocalDate targetDate) {
        // 参数有效性检查
        if (StrUtil.isBlank(strategy.getSpecificDates()) || targetDate == null) {
            log.debug("具体日期匹配失败：参数无效，specificDates={}, targetDate={}",
                     strategy.getSpecificDates(), targetDate);
            return false;
        }

        try {
            // 解析JSON格式的日期列表
            List<String> specificDates = JSON.parseArray(strategy.getSpecificDates(), String.class);
            String targetDateStr = targetDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            boolean matches = specificDates.contains(targetDateStr);

            log.debug("具体日期匹配结果：{} 在列表 {} 中 = {}",
                     targetDateStr, specificDates, matches);

            return matches;

        } catch (Exception e) {
            log.warn("解析具体日期JSON失败，策略ID: {}, JSON内容: {}",
                    strategy.getId(), strategy.getSpecificDates(), e);
            return false;
        }
    }

    /**
     * 从商品名称中提取日期
     * 商品名称格式: 大棕子的杂货店-情侣房-2025-07-27
     *
     * @param productName 商品名称
     * @return 提取的日期，如果提取失败返回null
     */
    private LocalDate extractDateFromProductName(String productName) {
        if (StringUtils.isBlank(productName)) {
            return null;
        }

        try {
            // 使用正则表达式提取日期 yyyy-MM-dd
            String datePattern = "\\d{4}-\\d{2}-\\d{2}";
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(datePattern);
            java.util.regex.Matcher matcher = pattern.matcher(productName);

            if (matcher.find()) {
                String dateStr = matcher.group();
                log.debug("从商品名称 {} 中提取到日期: {}", productName, dateStr);
                return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } else {
                log.debug("商品名称中未找到日期格式: {}", productName);
            }
        } catch (Exception e) {
            log.warn("从商品名称提取日期失败: {}", productName, e);
        }
        return null;
    }

    /**
     * 判断是否为酒店商品
     * 通过商品类型直接判断
     *
     * @param product 商品对象
     * @return true表示是酒店商品
     */
    private boolean isHotelProduct(Product product) {
        if (product == null) {
            return false;
        }

        // 直接通过商品类型判断：7=酒店房间
        return Integer.valueOf(7).equals(product.getType());
    }

    /**
     * 创建商品详情
     */
    private void createProductDescription(Integer productId, HotelRoom room, LocalDate checkInDate) {
        ProductDescription description = new ProductDescription();
        description.setProductId(productId);
        description.setType(7); // 酒店房间商品
        description.setMarketingType(0); // 基础商品

        StringBuilder content = new StringBuilder();
        content.append("<div class='hotel-product-detail'>");
        content.append("<h3>房型信息</h3>");
        content.append("<p><strong>房型名称：</strong>").append(room.getRoomName()).append("</p>");
        content.append("<p><strong>入住日期：</strong>").append(checkInDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))).append("</p>");
        content.append("<p><strong>房间面积：</strong>").append(room.getRoomArea()).append("平方米</p>");
        content.append("<p><strong>床型：</strong>").append(room.getBedType()).append("</p>");
        content.append("<p><strong>最大入住人数：</strong>").append(room.getMaxGuests()).append("人</p>");

        if (StrUtil.isNotBlank(room.getRoomDescription())) {
            content.append("<h3>房间描述</h3>");
            content.append("<p>").append(room.getRoomDescription()).append("</p>");
        }

        if (StrUtil.isNotBlank(room.getRoomFacilities())) {
            content.append("<h3>房间设施</h3>");
            try {
                List<String> facilities = JSON.parseArray(room.getRoomFacilities(), String.class);
                content.append("<ul>");
                for (String facility : facilities) {
                    content.append("<li>").append(facility).append("</li>");
                }
                content.append("</ul>");
            } catch (Exception e) {
                content.append("<p>").append(room.getRoomFacilities()).append("</p>");
            }
        }

        content.append("<h3>预订说明</h3>");
        content.append("<p>• 此商品为酒店预订商品，需到店核销</p>");
        content.append("<p>• 请在入住当日携带有效证件到酒店前台办理入住</p>");
        content.append("<p>• 如需取消或修改订单，请联系客服</p>");
        content.append("</div>");

        description.setDescription(content.toString());

        productDescriptionService.save(description);
    }

    /**
     * 创建商品属性值（单规格）
     */
    private void createProductAttrValue(Product product, HotelRoom room, LocalDate checkInDate) {
        ProductAttrValue attrValue = new ProductAttrValue();

        attrValue.setProductId(product.getId());
        attrValue.setSku(generateSku(room, checkInDate));
        attrValue.setStock(room.getTotalRooms());
        attrValue.setSales(0);
        attrValue.setPrice(product.getPrice());
        attrValue.setImage(product.getImage());
        attrValue.setCost(product.getCost());
        attrValue.setOtPrice(product.getOtPrice());
        attrValue.setWeight(BigDecimal.ZERO);
        attrValue.setVolume(BigDecimal.ZERO);

        // 分销设置
        attrValue.setBrokerage(10); // 一级分佣10%
        attrValue.setBrokerageTwo(5); // 二级分佣5%

        attrValue.setType(7); // 酒店房间商品
        attrValue.setAttrValue("{}"); // 单规格无属性
        attrValue.setIsDel(false);
        attrValue.setVersion(0);
        attrValue.setMasterId(0);
        attrValue.setIsCallback(false);
        attrValue.setVipPrice(BigDecimal.ZERO);
        attrValue.setMarketingType(0);
        attrValue.setRedeemIntegral(0);
        attrValue.setIsShow(true);
        attrValue.setIsDefault(true);
        attrValue.setItemNumber("");

        productAttrValueService.save(attrValue);
    }

    /**
     * 生成SKU编码
     */
    private String generateSku(HotelRoom room, LocalDate checkInDate) {
        return String.format("HOTEL_%d_%d_%s",
                           room.getMerId(),
                           room.getId(),
                           checkInDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
    }

    /**
     * 更新商品属性值
     */
    private void updateProductAttrValue(Integer productId, BigDecimal price, Integer stock) {
        LambdaQueryWrapper<ProductAttrValue> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductAttrValue::getProductId, productId)
               .eq(ProductAttrValue::getIsDel, false);

        ProductAttrValue attrValue = productAttrValueService.getOne(wrapper);
        if (attrValue != null) {
            attrValue.setPrice(price);
            attrValue.setStock(stock);
            attrValue.setOtPrice(price.multiply(new BigDecimal("1.2")));
            attrValue.setCost(price.multiply(new BigDecimal("0.8")));

            productAttrValueService.updateById(attrValue);
        }
    }

    /**
     * 更新商品属性值库存
     */
    private void updateProductAttrValueStock(Integer productId, Integer stock) {
        LambdaQueryWrapper<ProductAttrValue> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductAttrValue::getProductId, productId)
               .eq(ProductAttrValue::getIsDel, false);

        ProductAttrValue attrValue = productAttrValueService.getOne(wrapper);
        if (attrValue != null) {
            attrValue.setStock(stock);
            productAttrValueService.updateById(attrValue);
        }
    }

    /**
     * 更新商品属性值价格
     */
    private void updateProductAttrValuePrice(Integer productId, BigDecimal price) {
        LambdaQueryWrapper<ProductAttrValue> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductAttrValue::getProductId, productId)
               .eq(ProductAttrValue::getIsDel, false);

        ProductAttrValue attrValue = productAttrValueService.getOne(wrapper);
        if (attrValue != null) {
            attrValue.setPrice(price);
            attrValue.setOtPrice(price.multiply(new BigDecimal("1.2")));
            attrValue.setCost(price.multiply(new BigDecimal("0.8")));

            productAttrValueService.updateById(attrValue);
        }
    }

    // ==================== 日期转换工具方法 ====================


    /**
     * Date转LocalDate
     */
    private LocalDate dateToLocalDate(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    // ==================== 实时同步方法实现 ====================

    @Override
    @Caching(evict = {
        @CacheEvict(value = "hotel:room:details", allEntries = true),
        @CacheEvict(value = "hotel:room:list", allEntries = true),
        @CacheEvict(value = "hotel:list", allEntries = true)
    })
    public void syncRoomProductsRealTime(Integer roomId) {
        // 📊 开始监控
        String monitorId = realTimeSyncMonitor.recordSyncStart("room", roomId.toString());

        try {
            log.info("开始实时同步房型 {} 的商品", roomId);

            // 🚦 限流检查
            realTimeSyncLimiter.checkRoomSyncLimit(roomId);

            HotelRoom room = hotelRoomService.getById(roomId);
        if (room == null) {
            log.warn("房型 {} 不存在", roomId);
            return;
        }

        Merchant merchant = merchantService.getById(room.getMerId());
        if (merchant == null) {
            log.warn("商户 {} 不存在", room.getMerId());
            return;
        }

        // 查找该房型的所有商品（未来30天）
        LocalDate today = LocalDate.now();
        LocalDate endDate = today.plusDays(30);

        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getMerId, room.getMerId())
               .eq(Product::getType, 7) // 酒店商品类型
               .like(Product::getName, room.getRoomName())
               .eq(Product::getIsDel, false);

        List<Product> existingProducts = productService.list(wrapper);

        // 批量更新现有商品
        List<Product> productsToUpdate = new ArrayList<>();

        for (Product product : existingProducts) {
            try {
                // 从商品名称提取日期
                LocalDate productDate = extractDateFromProductName(product.getName());
                if (productDate != null && !productDate.isBefore(today) && !productDate.isAfter(endDate)) {

                    // 🔄 重新生成商品名称（包含最新特价标记）
                    String newProductName = generateProductName(merchant.getName(), room.getRoomName(), productDate, room);
                    product.setName(newProductName);

                    // 🔄 重新生成关键词（包含最新特价标记）
                    String newKeywords = generateProductKeywords(room, productDate);
                    product.setKeyword(newKeywords);

                    // 🔄 重新计算价格
                    BigDecimal newPrice = calculatePrice(room.getId(), productDate);
                    product.setPrice(newPrice);
                    product.setOtPrice(newPrice.multiply(new BigDecimal("1.2")));
                    product.setCost(newPrice.multiply(new BigDecimal("0.8")));

                    // 🔄 更新库存
                    product.setStock(room.getTotalRooms());

                    productsToUpdate.add(product);
                }
            } catch (Exception e) {
                log.error("更新商品失败，商品ID: {}", product.getId(), e);
            }
        }

        // 🚀 批量更新数据库（使用优化的批量更新方法）
        if (!productsToUpdate.isEmpty()) {
            try {
                // 使用优化的批量更新方法
                int updatedCount = productService.batchUpdateOptimized(productsToUpdate);
                log.info("批量更新商品成功，预期数量: {}, 实际更新数量: {}", productsToUpdate.size(), updatedCount);

                // 批量更新商品属性值价格
                for (Product product : productsToUpdate) {
                    updateProductAttrValuePrice(product.getId(), product.getPrice());
                }
            } catch (Exception e) {
                log.error("批量更新商品失败，回退到逐个更新", e);
                // 回退到原有的批量更新方法
                productService.updateBatchById(productsToUpdate);

                for (Product product : productsToUpdate) {
                    updateProductAttrValuePrice(product.getId(), product.getPrice());
                }
            }
        }

            log.info("房型 {} 实时同步完成，更新商品数: {}", roomId, productsToUpdate.size());

            // 📊 记录监控成功
            realTimeSyncMonitor.recordSyncEnd(monitorId, true, productsToUpdate.size(), null);

        } catch (Exception e) {
            log.error("房型 {} 实时同步失败", roomId, e);

            // 📊 记录监控失败
            realTimeSyncMonitor.recordSyncEnd(monitorId, false, 0, e.getMessage());

            throw e; // 重新抛出异常
        }
    }

    @Override
    public void syncRoomProductsByDateRange(Integer roomId, LocalDate startDate, LocalDate endDate) {
        log.info("开始实时同步房型 {} 的商品，日期范围: {} 到 {}", roomId, startDate, endDate);

        HotelRoom room = hotelRoomService.getById(roomId);
        if (room == null) {
            log.warn("房型 {} 不存在", roomId);
            return;
        }

        Merchant merchant = merchantService.getById(room.getMerId());
        if (merchant == null) {
            log.warn("商户 {} 不存在", room.getMerId());
            return;
        }

        // 查找该房型在指定日期范围内的商品
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getMerId, room.getMerId())
               .eq(Product::getType, 7) // 酒店商品类型
               .like(Product::getName, room.getRoomName())
               .eq(Product::getIsDel, false);

        List<Product> existingProducts = productService.list(wrapper);
        List<Product> productsToUpdate = new ArrayList<>();

        for (Product product : existingProducts) {
            try {
                // 从商品名称提取日期
                LocalDate productDate = extractDateFromProductName(product.getName());
                if (productDate != null &&
                    !productDate.isBefore(startDate) &&
                    !productDate.isAfter(endDate)) {

                    // 重新生成商品信息
                    String newProductName = generateProductName(merchant.getName(), room.getRoomName(), productDate, room);
                    product.setName(newProductName);

                    String newKeywords = generateProductKeywords(room, productDate);
                    product.setKeyword(newKeywords);

                    BigDecimal newPrice = calculatePrice(room.getId(), productDate);
                    product.setPrice(newPrice);
                    product.setOtPrice(newPrice.multiply(new BigDecimal("1.2")));
                    product.setCost(newPrice.multiply(new BigDecimal("0.8")));

                    product.setStock(room.getTotalRooms());

                    productsToUpdate.add(product);
                }
            } catch (Exception e) {
                log.error("更新商品失败，商品ID: {}", product.getId(), e);
            }
        }

        // 🚀 批量更新（使用优化的批量更新方法）
        if (!productsToUpdate.isEmpty()) {
            try {
                int updatedCount = productService.batchUpdateOptimized(productsToUpdate);
                log.info("日期范围批量更新商品成功，预期数量: {}, 实际更新数量: {}", productsToUpdate.size(), updatedCount);

                for (Product product : productsToUpdate) {
                    updateProductAttrValuePrice(product.getId(), product.getPrice());
                }
            } catch (Exception e) {
                log.error("日期范围批量更新商品失败，回退到逐个更新", e);
                productService.updateBatchById(productsToUpdate);

                for (Product product : productsToUpdate) {
                    updateProductAttrValuePrice(product.getId(), product.getPrice());
                }
            }
        }

        log.info("房型 {} 日期范围实时同步完成，更新商品数: {}", roomId, productsToUpdate.size());
    }

    @Override
    public void syncMultipleRoomsRealTime(List<Integer> roomIds) {
        log.info("开始批量实时同步房型，房型数量: {}", roomIds.size());

        for (Integer roomId : roomIds) {
            try {
                syncRoomProductsRealTime(roomId);
            } catch (Exception e) {
                log.error("批量同步房型 {} 失败", roomId, e);
            }
        }

        log.info("批量实时同步完成，房型数量: {}", roomIds.size());
    }

    @Override
    public void syncMerchantRoomsRealTime(Integer merchantId) {
        // 📊 开始监控
        String monitorId = realTimeSyncMonitor.recordSyncStart("merchant", merchantId.toString());

        try {
            log.info("开始实时同步商户 {} 的所有房型", merchantId);

            // 🚦 限流检查
            realTimeSyncLimiter.checkMerchantSyncLimit(merchantId);

        // 获取商户的所有启用房型
        LambdaQueryWrapper<HotelRoom> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HotelRoom::getMerId, merchantId)
               .eq(HotelRoom::getStatus, 1) // 启用状态
               .eq(HotelRoom::getIsDel, 0); // 未删除

        List<HotelRoom> rooms = hotelRoomService.list(wrapper);

        if (rooms.isEmpty()) {
            log.info("商户 {} 没有启用的房型", merchantId);
            return;
        }

        List<Integer> roomIds = rooms.stream()
                .map(HotelRoom::getId)
                .collect(Collectors.toList());

            syncMultipleRoomsRealTime(roomIds);

            log.info("商户 {} 所有房型实时同步完成，房型数量: {}", merchantId, roomIds.size());

            // 📊 记录监控成功
            realTimeSyncMonitor.recordSyncEnd(monitorId, true, roomIds.size(), null);

        } catch (Exception e) {
            log.error("商户 {} 所有房型实时同步失败", merchantId, e);

            // 📊 记录监控失败
            realTimeSyncMonitor.recordSyncEnd(monitorId, false, 0, e.getMessage());

            throw e; // 重新抛出异常
        }
    }
}
