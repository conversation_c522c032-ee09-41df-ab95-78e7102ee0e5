package com.zbkj.service.service.hotel;

import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 实时同步限流器
 * 防止频繁调用实时同步接口，保护系统性能
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Slf4j
@Component
public class RealTimeSyncLimiter {

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 尝试获取同步许可
     * 
     * @param key 限流键
     * @param maxRequests 时间窗口内最大请求数
     * @param timeWindowSeconds 时间窗口（秒）
     * @return 是否获取成功
     */
    public boolean tryAcquire(String key, int maxRequests, int timeWindowSeconds) {
        String redisKey = "sync_limit:" + key;
        
        try {
            Integer current = (Integer) redisUtil.get(redisKey);
            
            if (current == null) {
                // 首次请求，设置计数器
                redisUtil.set(redisKey, 1, timeWindowSeconds);
                log.debug("限流器首次请求，key: {}, 剩余次数: {}", key, maxRequests - 1);
                return true;
            } else if (current < maxRequests) {
                // 未达到限制，增加计数
                redisUtil.incr(redisKey, 1);
                log.debug("限流器允许请求，key: {}, 当前次数: {}, 剩余次数: {}", 
                         key, current + 1, maxRequests - current - 1);
                return true;
            } else {
                // 达到限制，拒绝请求
                log.warn("限流器拒绝请求，key: {}, 当前次数: {}, 最大次数: {}", 
                        key, current, maxRequests);
                return false;
            }
        } catch (Exception e) {
            log.error("限流器异常，key: {}", key, e);
            // 异常时允许通过，避免影响业务
            return true;
        }
    }

    /**
     * 检查房型同步限流
     * 每个房型每分钟最多同步10次
     * 
     * @param roomId 房型ID
     * @throws CrmebException 超过限流时抛出异常
     */
    public void checkRoomSyncLimit(Integer roomId) {
        if (!tryAcquire("room:" + roomId, 10, 60)) {
            throw new CrmebException("房型 " + roomId + " 同步过于频繁，请稍后再试");
        }
    }

    /**
     * 检查商户同步限流
     * 每个商户每5分钟最多同步5次
     * 
     * @param merchantId 商户ID
     * @throws CrmebException 超过限流时抛出异常
     */
    public void checkMerchantSyncLimit(Integer merchantId) {
        if (!tryAcquire("merchant:" + merchantId, 5, 300)) {
            throw new CrmebException("商户 " + merchantId + " 同步过于频繁，请稍后再试");
        }
    }

    /**
     * 检查批量同步限流
     * 每个IP每分钟最多批量同步3次
     * 
     * @param ipAddress IP地址
     * @throws CrmebException 超过限流时抛出异常
     */
    public void checkBatchSyncLimit(String ipAddress) {
        if (!tryAcquire("batch:" + ipAddress, 3, 60)) {
            throw new CrmebException("批量同步过于频繁，请稍后再试");
        }
    }

    /**
     * 获取剩余同步次数
     * 
     * @param key 限流键
     * @param maxRequests 最大请求数
     * @return 剩余次数
     */
    public int getRemainingCount(String key, int maxRequests) {
        String redisKey = "sync_limit:" + key;
        
        try {
            Integer current = (Integer) redisUtil.get(redisKey);
            if (current == null) {
                return maxRequests;
            } else {
                return Math.max(0, maxRequests - current);
            }
        } catch (Exception e) {
            log.error("获取剩余次数异常，key: {}", key, e);
            return maxRequests;
        }
    }

    /**
     * 清除限流记录（管理员操作）
     * 
     * @param key 限流键
     */
    public void clearLimit(String key) {
        String redisKey = "sync_limit:" + key;
        redisUtil.del(redisKey);
        log.info("清除限流记录，key: {}", key);
    }
}
