package com.zbkj.service.service.hotel;

import com.zbkj.common.model.product.Product;
import com.zbkj.service.dao.ProductDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 智能批量更新服务
 * 根据更新内容和数据量智能选择最优的更新策略
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Slf4j
@Service
public class SmartBatchUpdateService {

    @Autowired
    private ProductDao productDao;

    /**
     * 智能批量更新商品
     * 
     * @param products 商品列表
     * @param updateType 更新类型：ALL(全字段), PRICE(仅价格), NAME_KEYWORD(名称关键词)
     * @return 更新成功的数量
     */
    public int smartBatchUpdate(List<Product> products, UpdateType updateType) {
        if (products == null || products.isEmpty()) {
            log.warn("批量更新商品列表为空");
            return 0;
        }

        log.info("🚀 智能批量更新开始，数量: {}, 类型: {}", products.size(), updateType);
        
        try {
            // 根据更新类型和数据量选择策略
            return switch (updateType) {
                case PRICE -> batchUpdatePricesOnly(products);
                case NAME_KEYWORD -> batchUpdateNameAndKeyword(products);
                case ALL -> batchUpdateAllFields(products);
            };
            
        } catch (Exception e) {
            log.error("智能批量更新失败，数量: {}, 类型: {}", products.size(), updateType, e);
            throw e;
        }
    }

    /**
     * 仅更新价格相关字段
     */
    private int batchUpdatePricesOnly(List<Product> products) {
        log.info("执行价格专用批量更新，数量: {}", products.size());
        
        if (products.size() <= 50) {
            // 小批量：直接使用价格专用SQL
            return productDao.batchUpdateProductPrices(products);
        } else {
            // 大批量：分批处理
            return processBatchUpdate(products, 30, 
                batch -> productDao.batchUpdateProductPrices(batch));
        }
    }

    /**
     * 更新名称和关键词
     */
    private int batchUpdateNameAndKeyword(List<Product> products) {
        log.info("执行名称关键词批量更新，数量: {}", products.size());
        
        // 创建只包含名称和关键词变更的商品列表
        List<Product> nameKeywordProducts = products.stream()
                .map(this::createNameKeywordOnlyProduct)
                .toList();
        
        if (products.size() <= 40) {
            return productDao.batchUpdateProducts(nameKeywordProducts);
        } else {
            return processBatchUpdate(nameKeywordProducts, 25, 
                batch -> productDao.batchUpdateProducts(batch));
        }
    }

    /**
     * 更新所有字段
     */
    private int batchUpdateAllFields(List<Product> products) {
        log.info("执行全字段批量更新，数量: {}", products.size());
        
        if (products.size() <= 20) {
            // 小批量：直接更新
            return productDao.batchUpdateProducts(products);
        } else {
            // 大批量：分批处理，批次更小
            return processBatchUpdate(products, 15, 
                batch -> productDao.batchUpdateProducts(batch));
        }
    }

    /**
     * 通用分批处理方法
     */
    private int processBatchUpdate(List<Product> products, int batchSize, 
                                  BatchUpdateFunction updateFunction) {
        int totalUpdated = 0;
        
        for (int i = 0; i < products.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, products.size());
            List<Product> batch = products.subList(i, endIndex);
            
            try {
                int updated = updateFunction.apply(batch);
                totalUpdated += updated;
                
                log.debug("批次更新完成: {}-{}, 更新数量: {}", i + 1, endIndex, updated);
                
                // 批次间休息，避免数据库压力
                if (endIndex < products.size()) {
                    Thread.sleep(5); // 5ms休息
                }
                
            } catch (Exception e) {
                log.error("批次更新失败: {}-{}", i + 1, endIndex, e);
                // 单个批次失败不影响其他批次
            }
        }
        
        log.info("分批更新完成，总数量: {}, 更新数量: {}", products.size(), totalUpdated);
        return totalUpdated;
    }

    /**
     * 创建只包含名称和关键词的商品对象
     */
    private Product createNameKeywordOnlyProduct(Product original) {
        Product product = new Product();
        product.setId(original.getId());
        product.setName(original.getName());
        product.setKeyword(original.getKeyword());
        return product;
    }

    /**
     * 检查商品是否需要更新
     */
    public boolean needsUpdate(Product oldProduct, Product newProduct) {
        if (oldProduct == null || newProduct == null) {
            return false;
        }

        return !Objects.equals(oldProduct.getName(), newProduct.getName()) ||
               !Objects.equals(oldProduct.getKeyword(), newProduct.getKeyword()) ||
               !Objects.equals(oldProduct.getPrice(), newProduct.getPrice()) ||
               !Objects.equals(oldProduct.getOtPrice(), newProduct.getOtPrice()) ||
               !Objects.equals(oldProduct.getCost(), newProduct.getCost()) ||
               !Objects.equals(oldProduct.getStock(), newProduct.getStock());
    }

    /**
     * 过滤出真正需要更新的商品
     */
    public List<Product> filterChangedProducts(List<Product> oldProducts, List<Product> newProducts) {
        List<Product> changedProducts = new ArrayList<>();
        
        for (Product newProduct : newProducts) {
            Product oldProduct = oldProducts.stream()
                    .filter(p -> Objects.equals(p.getId(), newProduct.getId()))
                    .findFirst()
                    .orElse(null);
            
            if (needsUpdate(oldProduct, newProduct)) {
                changedProducts.add(newProduct);
            }
        }
        
        log.info("过滤变更商品，原始数量: {}, 变更数量: {}", newProducts.size(), changedProducts.size());
        return changedProducts;
    }

    /**
     * 检测更新类型
     */
    public UpdateType detectUpdateType(List<Product> products) {
        if (products.isEmpty()) {
            return UpdateType.ALL;
        }

        // 简单检测：如果所有商品的名称都包含特价标记相关内容，认为是名称关键词更新
        boolean hasSpecialOfferChanges = products.stream()
                .anyMatch(p -> p.getName() != null && 
                         (p.getName().contains("[特价]") || p.getName().contains("[周末不加价]")));
        
        if (hasSpecialOfferChanges) {
            return UpdateType.NAME_KEYWORD;
        }

        // 如果价格有变化，认为是价格更新
        boolean hasPriceChanges = products.stream()
                .anyMatch(p -> p.getPrice() != null && p.getPrice().compareTo(BigDecimal.ZERO) > 0);
        
        if (hasPriceChanges) {
            return UpdateType.PRICE;
        }

        return UpdateType.ALL;
    }

    /**
     * 更新类型枚举
     */
    public enum UpdateType {
        ALL("全字段更新"),
        PRICE("仅价格更新"), 
        NAME_KEYWORD("名称关键词更新");

        private final String description;

        UpdateType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 批量更新函数接口
     */
    @FunctionalInterface
    private interface BatchUpdateFunction {
        int apply(List<Product> batch) throws Exception;
    }
}
