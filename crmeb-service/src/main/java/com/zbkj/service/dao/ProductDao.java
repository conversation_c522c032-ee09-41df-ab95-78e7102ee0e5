package com.zbkj.service.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zbkj.common.model.product.Product;
import com.zbkj.common.response.PlatformProductListResponse;
import com.zbkj.common.response.ProductActivityResponse;
import com.zbkj.common.response.ProductFrontResponse;
import com.zbkj.common.response.ProductMarketingResponse;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 商品表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-19
 */
public interface ProductDao extends BaseMapper<Product> {
    /**
     * 平台端商品分页列表
     * @param map 查询参数
     */
    List<PlatformProductListResponse> getPlatformPageList(Map<String, Object> map);

    /**
     * 移动端商品列表
     * @param map 查询参数
     */
    List<ProductFrontResponse> findH5List(Map<String, Object> map);

    /**
     * 商品搜索分页列表（活动）
     * @param map 查询参数
     */
    List<ProductActivityResponse> getActivitySearchPage(Map<String, Object> map);

    /**
     * 商品搜索分页列表（营销）
     * @param map 查询参数
     */
    List<ProductMarketingResponse> getMarketingSearchPage(Map<String, Object> map);

    /**
     * 商品搜索分页列表（活动）商户
     * @param map 查询参数
     */
    List<ProductActivityResponse> getActivitySearchPageByMerchant(Map<String, Object> map);

    /**
     * 商品搜索分页列表（营销）商户
     * @param map 查询参数
     */
    List<ProductMarketingResponse> getMarketingSearchPageByMerchant(Map<String, Object> map);

    /**
     * 根据关键字获取商品所有的品牌ID
     * @param keyword 关键字
     */
    List<Integer> findProductBrandIdByKeyword(@Param(value = "keyword") String keyword);

    /**
     * 根据关键字获取商品所有的分类ID
     * @param keyword 关键字
     */
    List<Integer> findProductCategoryIdByKeyword(@Param(value = "keyword") String keyword);

    /**
     * 平台端商品列表表头数量
     * @param map 查询参数
     */
    Integer getPlatformPageCount(Map<String, Object> map);

    /**
     * 批量更新商品信息（优化版本）
     * 使用CASE WHEN语句进行批量更新，提高性能
     *
     * @param products 商品列表
     * @return 更新的记录数
     */
    @Update("<script>" +
            "UPDATE eb_product SET " +
            "name = CASE id " +
            "<foreach collection='products' item='item'>" +
            "WHEN #{item.id} THEN #{item.name} " +
            "</foreach>" +
            "END, " +
            "keyword = CASE id " +
            "<foreach collection='products' item='item'>" +
            "WHEN #{item.id} THEN #{item.keyword} " +
            "</foreach>" +
            "END, " +
            "price = CASE id " +
            "<foreach collection='products' item='item'>" +
            "WHEN #{item.id} THEN #{item.price} " +
            "</foreach>" +
            "END, " +
            "ot_price = CASE id " +
            "<foreach collection='products' item='item'>" +
            "WHEN #{item.id} THEN #{item.otPrice} " +
            "</foreach>" +
            "END, " +
            "cost = CASE id " +
            "<foreach collection='products' item='item'>" +
            "WHEN #{item.id} THEN #{item.cost} " +
            "</foreach>" +
            "END, " +
            "stock = CASE id " +
            "<foreach collection='products' item='item'>" +
            "WHEN #{item.id} THEN #{item.stock} " +
            "</foreach>" +
            "END " +
            "WHERE id IN " +
            "<foreach collection='products' item='item' open='(' separator=',' close=')'>" +
            "#{item.id}" +
            "</foreach>" +
            "</script>")
    int batchUpdateProducts(@Param("products") List<Product> products);
}
